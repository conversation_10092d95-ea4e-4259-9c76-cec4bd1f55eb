import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../../../../lib/core/shared/enums/entry_enums.dart';
import '../../../../../lib/features/meter_readings/domain/models/meter_reading.dart';
import '../../../../../lib/features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../../../lib/features/top_ups/domain/models/top_up.dart';
import '../../../../../lib/features/top_ups/domain/repositories/top_up_repository.dart';
import '../../../../../lib/features/validation/domain/models/validation_issue.dart';
import '../../../../../lib/features/validation/domain/services/dismissed_entry_service.dart';
import '../../../../../lib/features/validation/domain/services/simple_gap_detection_service.dart';

import 'dismissed_entry_service_test.mocks.dart';

@GenerateMocks([MeterReadingRepository])
void main() {
  group('DismissedEntryService', () {
    late DismissedEntryService service;
    late MockMeterReadingRepository mockRepository;

    setUp(() {
      mockRepository = MockMeterReadingRepository();
      service = DismissedEntryService(mockRepository);
    });

    group('isDismissedEntry', () {
      test('returns true for dismissed entry', () {
        final reading = MeterReading(
          value: 0.0,
          date: DateTime.now(),
          status: EntryStatus.ignored,
          notes:
              'Dismissed missing entry gap: 70 days (01/01/2024 - 12/03/2024)',
        );

        expect(service.isDismissedEntry(reading), isTrue);
      });

      test('returns false for normal entry', () {
        final reading = MeterReading(
          value: 100.0,
          date: DateTime.now(),
          status: EntryStatus.valid,
          notes: 'Normal reading',
        );

        expect(service.isDismissedEntry(reading), isFalse);
      });

      test('returns false for entry with wrong status', () {
        final reading = MeterReading(
          value: 0.0,
          date: DateTime.now(),
          status: EntryStatus.valid,
          notes: 'Dismissed missing entry gap: 70 days',
        );

        expect(service.isDismissedEntry(reading), isFalse);
      });

      test('returns false for entry with wrong value', () {
        final reading = MeterReading(
          value: 50.0,
          date: DateTime.now(),
          status: EntryStatus.ignored,
          notes: 'Dismissed missing entry gap: 70 days',
        );

        expect(service.isDismissedEntry(reading), isFalse);
      });

      test('returns false for entry without dismissal notes', () {
        final reading = MeterReading(
          value: 0.0,
          date: DateTime.now(),
          status: EntryStatus.ignored,
          notes: 'Some other notes',
        );

        expect(service.isDismissedEntry(reading), isFalse);
      });
    });

    group('isGapDismissed', () {
      test('returns true when gap has dismissed entry', () {
        final start = DateTime(2024, 1, 1);
        final end = DateTime(2024, 3, 12);
        final dismissedEntry = MeterReading(
          value: 0.0,
          date: DateTime(2024, 2, 1),
          status: EntryStatus.ignored,
          notes: 'Dismissed missing entry gap: 70 days',
        );

        final readings = [dismissedEntry];

        expect(service.isGapDismissed(readings, start, end), isTrue);
      });

      test('returns false when gap has no dismissed entry', () {
        final start = DateTime(2024, 1, 1);
        final end = DateTime(2024, 3, 12);
        final normalEntry = MeterReading(
          value: 100.0,
          date: DateTime(2024, 2, 1),
          status: EntryStatus.valid,
          notes: 'Normal reading',
        );

        final readings = [normalEntry];

        expect(service.isGapDismissed(readings, start, end), isFalse);
      });
    });

    group('createDismissalEntry', () {
      test('creates dismissal entry successfully', () async {
        final issue = ValidationIssue(
          type: ValidationIssueType.missingEntry,
          severity: ValidationIssueSeverity.low,
          message: 'Missing entry detected',
          metadata: {
            'start_date': '2024-01-01T00:00:00.000Z',
            'end_date': '2024-03-12T00:00:00.000Z',
            'gap_days': 70,
          },
        );

        when(mockRepository.addMeterReading(any)).thenAnswer((_) async => 1);

        final result = await service.createDismissalEntry(issue);

        expect(result.value, equals(0.0));
        expect(result.status, equals(EntryStatus.ignored));
        expect(result.notes, contains('Dismissed missing entry gap'));
        expect(
            result.date, equals(DateTime(2024, 3, 3))); // 62 days after start

        verify(mockRepository.addMeterReading(any)).called(1);
      });

      test('throws exception for gap too short', () async {
        final issue = ValidationIssue(
          type: ValidationIssueType.missingEntry,
          severity: ValidationIssueSeverity.low,
          message: 'Missing entry detected',
          metadata: {
            'start_date': '2024-01-01T00:00:00.000Z',
            'end_date': '2024-01-30T00:00:00.000Z', // Only 29 days gap
            'gap_days': 29,
          },
        );

        expect(
          () => service.createDismissalEntry(issue),
          throwsA(isA<Exception>()),
        );

        verifyNever(mockRepository.addMeterReading(any));
      });
    });
  });
}

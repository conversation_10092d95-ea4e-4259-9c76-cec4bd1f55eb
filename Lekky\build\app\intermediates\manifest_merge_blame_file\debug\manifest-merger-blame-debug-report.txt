1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lekky.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- Add permissions for notifications -->
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Add permission for exact alarms (Android 12+ / API 31+) -->
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" /> <!-- Add permission for boot completed to reschedule notifications -->
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-78
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-76
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Add permissions for background work -->
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-80
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-78
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:5-67
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:22-65
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Add permission to ignore battery optimizations -->
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:5-76
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:22-74
22    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- Add permission for alarm clock notifications -->
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:13:5-94
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:13:22-92
23    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- Storage permissions for file export -->
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:15:5-73
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:15:22-71
24    <uses-permission
24-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:18:5-20:38
25        android:name="android.permission.READ_EXTERNAL_STORAGE"
25-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:19:9-64
26        android:maxSdkVersion="32" />
26-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:20:9-35
27    <uses-permission
27-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:21:5-23:38
28        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
28-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:22:9-65
29        android:maxSdkVersion="29" />
29-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:23:9-35
30    <!--
31 Required to query activities that can process text, see:
32         https://developer.android.com/training/package-visibility?hl=en and
33         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
34
35         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
36    -->
37    <queries>
37-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:83:5-100:15
38        <intent>
38-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:84:9-87:18
39            <action android:name="android.intent.action.PROCESS_TEXT" />
39-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:85:13-72
39-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:85:21-70
40
41            <data android:mimeType="text/plain" />
41-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:13-50
41-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:19-48
42        </intent>
43        <!-- For document selection -->
44        <intent>
44-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:89:9-91:18
45            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
45-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:90:13-79
45-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:90:21-76
46        </intent>
47        <intent>
47-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:92:9-95:18
48            <action android:name="android.intent.action.CREATE_DOCUMENT" />
48-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:93:13-76
48-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:93:21-73
49
50            <data android:mimeType="text/csv" />
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:13-50
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:19-48
51        </intent>
52        <intent>
52-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:96:9-99:18
53            <action android:name="android.intent.action.OPEN_DOCUMENT" />
53-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:97:13-74
53-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:97:21-71
54
55            <data android:mimeType="text/csv" />
55-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:13-50
55-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:19-48
56        </intent>
57        <intent>
57-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
58            <action android:name="android.intent.action.GET_CONTENT" />
58-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
58-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
59
60            <data android:mimeType="*/*" />
60-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:13-50
60-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:86:19-48
61        </intent>
62    </queries>
63
64    <uses-permission android:name="android.permission.VIBRATE" />
64-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-66
64-->[:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-63
65    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
65-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
65-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
66
67    <permission
67-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
68        android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
72    <!--
73 Note: For Android 10+ (API 29+), we use Storage Access Framework (SAF)
74         which doesn't require additional permissions for user-selected locations
75    -->
76    <application
77        android:name="android.app.Application"
78        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
78-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\684577351670909f117ab3c5c378ca3b\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
79        android:debuggable="true"
80        android:enableOnBackInvokedCallback="true"
81        android:icon="@mipmap/ic_launcher"
82        android:label="lekky"
83        android:requestLegacyExternalStorage="true" >
84        <activity
85            android:name="com.lekky.app.MainActivity"
86            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
87            android:exported="true"
88            android:hardwareAccelerated="true"
89            android:launchMode="singleTop"
90            android:theme="@style/LaunchTheme"
91            android:windowSoftInputMode="adjustResize" >
92
93            <!--
94                 Specifies an Android theme to apply to this Activity as soon as
95                 the Android process has started. This theme is visible to the user
96                 while the Flutter UI initializes. After that, this theme continues
97                 to determine the Window background behind the Flutter UI.
98            -->
99            <meta-data
100                android:name="io.flutter.embedding.android.NormalTheme"
101                android:resource="@style/NormalTheme" />
102
103            <intent-filter>
104                <action android:name="android.intent.action.MAIN" />
105
106                <category android:name="android.intent.category.LAUNCHER" />
107            </intent-filter>
108        </activity>
109        <!--
110             Don't delete the meta-data below.
111             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
112        -->
113        <meta-data
114            android:name="flutterEmbedding"
115            android:value="2" />
116
117        <!-- Add receiver for boot completed to reschedule notifications -->
118        <receiver
119            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
120            android:exported="true" >
121            <intent-filter>
122                <action android:name="android.intent.action.BOOT_COMPLETED" />
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
123                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
124            </intent-filter>
125        </receiver>
126
127        <!-- Package name for the application -->
128        <meta-data
129            android:name="com.lekky.app.PACKAGE_NAME"
130            android:value="com.lekky.app" />
131
132        <activity
132-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-13:74
133            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
133-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
134            android:exported="false"
134-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
135            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
135-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-71
136
137        <provider
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
138            android:name="androidx.startup.InitializationProvider"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
139            android:authorities="com.lekky.app.androidx-startup"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
140            android:exported="false" >
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
141            <meta-data
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
142                android:name="androidx.work.WorkManagerInitializer"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
143                android:value="androidx.startup" />
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
144        </provider>
145
146        <service
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
147            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
148            android:directBootAware="false"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
149            android:enabled="@bool/enable_system_alarm_service_default"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
150            android:exported="false" />
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
151        <service
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
152            android:name="androidx.work.impl.background.systemjob.SystemJobService"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
154            android:enabled="@bool/enable_system_job_service_default"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
155            android:exported="true"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
156            android:permission="android.permission.BIND_JOB_SERVICE" />
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
157        <service
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
158            android:name="androidx.work.impl.foreground.SystemForegroundService"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
159            android:directBootAware="false"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
160            android:enabled="@bool/enable_system_foreground_service_default"
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
161            android:exported="false" />
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
162
163        <receiver
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
164            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
166            android:enabled="true"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
167            android:exported="false" />
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
168        <receiver
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
169            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
171            android:enabled="false"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
173            <intent-filter>
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
174                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
175                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
176            </intent-filter>
177        </receiver>
178        <receiver
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
179            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
181            android:enabled="false"
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
183            <intent-filter>
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
184                <action android:name="android.intent.action.BATTERY_OKAY" />
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
185                <action android:name="android.intent.action.BATTERY_LOW" />
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
191            android:enabled="false"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
194                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
195                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
196            </intent-filter>
197        </receiver>
198        <receiver
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
199            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
201            android:enabled="false"
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
203            <intent-filter>
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
204                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
205            </intent-filter>
206        </receiver>
207        <receiver
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
208            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
209            android:directBootAware="false"
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
210            android:enabled="false"
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
211            android:exported="false" >
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
212            <intent-filter>
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
213                <action android:name="android.intent.action.BOOT_COMPLETED" />
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
214                <action android:name="android.intent.action.TIME_SET" />
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
215                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
216            </intent-filter>
217        </receiver>
218        <receiver
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
219            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
221            android:enabled="@bool/enable_system_alarm_service_default"
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
222            android:exported="false" >
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
223            <intent-filter>
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
224                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
225            </intent-filter>
226        </receiver>
227        <receiver
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
228            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
229            android:directBootAware="false"
229-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
230            android:enabled="true"
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
231            android:exported="true"
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
232            android:permission="android.permission.DUMP" >
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
233            <intent-filter>
233-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
234                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
234-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
234-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\2a5a688d87ded2dd8a90eb4f6288faf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
235            </intent-filter>
236        </receiver>
237
238        <service
238-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
239            android:name="androidx.room.MultiInstanceInvalidationService"
239-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
240            android:directBootAware="true"
240-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
241            android:exported="false" />
241-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\00ee8946583275229f58c64a5ecbb412\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
242
243        <uses-library
243-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
244            android:name="androidx.window.extensions"
244-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
245            android:required="false" />
245-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
246        <uses-library
246-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
247            android:name="androidx.window.sidecar"
247-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
248            android:required="false" />
248-->[androidx.window:window:1.0.0-beta04] C:\Users\<USER>\.gradle\caches\transforms-3\d8adab4e681d0863a25dfe4be8db95bf\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
249    </application>
250
251</manifest>

// File: lib/core/utils/event_bus.dart
import 'dart:async';

/// Event types for the application
enum EventType {
  /// Data has been updated (entry added, edited, or deleted)
  dataUpdated,

  /// Averages are being calculated (show loading state)
  averagesCalculating,

  /// Average calculation failed
  averageCalculationFailed,

  /// Settings have been updated (generic)
  settingsUpdated,

  /// Date settings have been updated (date format or date info)
  dateSettingsUpdated,

  /// Alert settings have been updated (threshold, days in advance)
  alertSettingsUpdated,

  /// Reminder settings have been updated
  reminderSettingsUpdated,

  /// Notification settings have been updated
  notificationSettingsUpdated,

  /// Notification created (triggers immediate provider refresh)
  notificationCreated,

  /// Currency settings have been updated
  currencyUpdated,

  /// Data backup menu should be opened
  dataBackupMenuRequested,

  /// Dashboard periodic refresh (30-minute timer)
  dashboardPeriodicRefresh,

  /// Top-up added (immediate dashboard response needed)
  topUpAdded,
}

/// A simple event bus for communication between different parts of the app
class EventBus {
  // Singleton pattern
  static final EventBus _instance = EventBus._internal();
  factory EventBus() => _instance;
  EventBus._internal();

  // Stream controller for events
  final StreamController<EventType> _controller =
      StreamController<EventType>.broadcast();

  /// Get the stream of events
  Stream<EventType> get stream => _controller.stream;

  /// Fire an event
  void fire(EventType event) {
    _controller.add(event);
  }

  /// Dispose the event bus
  void dispose() {
    _controller.close();
  }
}

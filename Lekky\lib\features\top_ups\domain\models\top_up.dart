import '../../../../core/shared/enums/entry_enums.dart';

/// Model class for top-ups
class TopUp {
  /// Unique identifier
  final int? id;

  /// Top-up amount in currency
  final double amount;

  /// Date of the top-up
  final DateTime date;

  /// Entry status (valid, invalid)
  final EntryStatus status;

  /// Optional notes
  final String? notes;

  /// Optional payment method
  final String? paymentMethod;

  /// Creation date
  final DateTime createdAt;

  /// Last update date
  final DateTime updatedAt;

  /// Constructor
  TopUp({
    this.id,
    required this.amount,
    required this.date,
    this.status = EntryStatus.valid,
    this.notes,
    this.paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Convenience getter for backward compatibility
  bool get isValid => status == EntryStatus.valid;

  /// Check if entry is invalid
  bool get isInvalid => status == EntryStatus.invalid;

  /// Create a copy of this top-up with optional new values
  TopUp copyWith({
    int? id,
    double? amount,
    DateTime? date,
    EntryStatus? status,
    bool? isValid,
    String? notes,
    String? paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TopUp(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      status: status ??
          (isValid != null
              ? (isValid ? EntryStatus.valid : EntryStatus.invalid)
              : this.status),
      notes: notes ?? this.notes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Convert to a map for database operations
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'amount': amount,
      'date': date.toIso8601String(),
      // Use both status and is_valid fields for compatibility
      'status': status.value,
      'is_valid': isValid ? 1 : 0,
      'notes': notes,
      'payment_method': paymentMethod,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a top-up from a map
  factory TopUp.fromMap(Map<String, dynamic> map) {
    // Prioritize status field for dismissal entries, fallback to is_valid
    EntryStatus status;
    if (map.containsKey('status')) {
      status = EntryStatus.fromValue(map['status'] as int);
    } else if (map.containsKey('is_valid')) {
      status = (map['is_valid'] as int) == 1
          ? EntryStatus.valid
          : EntryStatus.invalid;
    } else {
      // Default to valid if neither field exists
      status = EntryStatus.valid;
    }

    return TopUp(
      id: map['id'] as int?,
      amount: map['amount'] as double,
      date: DateTime.parse(map['date'] as String),
      status: status,
      notes: map['notes'] as String?,
      paymentMethod: map['payment_method'] as String?,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  @override
  String toString() {
    return 'TopUp(id: $id, amount: $amount, date: $date, status: $status, notes: $notes, paymentMethod: $paymentMethod)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TopUp &&
        other.id == id &&
        other.amount == amount &&
        other.date.isAtSameMomentAs(date) &&
        other.notes == notes &&
        other.paymentMethod == paymentMethod;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        amount.hashCode ^
        date.hashCode ^
        notes.hashCode ^
        paymentMethod.hashCode;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/validation_provider.dart';
import '../controllers/validation_dashboard_controller.dart';
import '../widgets/issue_card.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/di/service_locator.dart';
import '../../domain/models/validation_issue.dart';
import '../../domain/models/integrity_report.dart';
import '../../domain/services/dismissed_entry_service.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../entries/presentation/dialogs/edit_entry_dialog.dart';
import '../../../../core/providers/date_formatter_provider.dart';

/// Dialog for viewing and managing validation issues
class ValidationDialog extends ConsumerStatefulWidget {
  const ValidationDialog({super.key});

  @override
  ConsumerState<ValidationDialog> createState() => _ValidationDialogState();
}

class _ValidationDialogState extends ConsumerState<ValidationDialog> {
  @override
  void initState() {
    super.initState();
    // Ensure data is refreshed when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(validationControllerProvider.notifier).refresh();
    });
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    debugPrint(
        'ValidationDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  @override
  Widget build(BuildContext context) {
    final validationState = ref.watch(validationControllerProvider);
    final validationNotifier = ref.read(validationControllerProvider.notifier);

    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28, // Same as notification dialog for 7% increased height
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // App Banner with validation colors and close button
          Stack(
            children: [
              AppBanner(
                message: 'Entry Validation',
                icon: Icons.check_circle,
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
                onDismiss: () => Navigator.of(context).pop(),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              // Filter icon positioned in banner area
              Positioned(
                top: 12,
                right: 56, // Position left of close button
                child: _buildFilterIcon(context, validationState),
              ),
              // Clear filters icon positioned in banner area
              Positioned(
                top: 12,
                right: 96, // Position left of filter icon
                child: IconButton(
                  icon: const Icon(
                    Icons.filter_list_off,
                    color: Colors.white,
                    size: 24,
                  ),
                  onPressed: validationNotifier.clearFilters,
                  tooltip: 'Clear Filters',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ),
            ],
          ),
          // Main content based on validation state
          Expanded(
            child: _buildValidationContent(context, validationState),
          ),
        ],
      ),
    );
  }

  /// Build the filter icon with dynamic styling and animation
  Widget _buildFilterIcon(BuildContext context, ValidationState state) {
    final filterColor = _getFilterIconColor(state.filterType);
    final isFilterActive = state.filterType != ValidationIssueFilterType.all;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: EdgeInsets.zero,
      decoration: isFilterActive
          ? BoxDecoration(
              color: filterColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      child: AnimatedScale(
        scale: isFilterActive ? 1.1 : 1.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: IconButton(
          icon: Icon(
            Icons.tune,
            color: isFilterActive ? filterColor : Colors.white,
            size: 24,
          ),
          onPressed: () => _showFilterDialog(context),
          tooltip: isFilterActive ? 'Filters applied' : 'Filter issues',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ),
    );
  }

  /// Get filter icon color based on filter type
  Color _getFilterIconColor(ValidationIssueFilterType filterType) {
    switch (filterType) {
      case ValidationIssueFilterType.all:
        return Colors.white;
      case ValidationIssueFilterType.highSeverity:
        return Colors.red;
      case ValidationIssueFilterType.mediumSeverity:
        return Colors.orange;
      case ValidationIssueFilterType.lowSeverity:
        return Colors.blue;
    }
  }

  /// Build validation content
  Widget _buildValidationContent(
    BuildContext context,
    ValidationState state,
  ) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.errorMessage != null) {
      return _buildErrorContent(context, state);
    }

    if (state.filteredIssues.isEmpty) {
      return _buildEmptyState(context, state);
    }

    return Column(
      children: [
        // Summary card
        if (state.integrityReport != null)
          _buildSummaryCard(state.integrityReport!),

        // Issues list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.filteredIssues.length,
            itemBuilder: (context, index) {
              final issue = state.filteredIssues[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: IssueCard(
                  issue: issue,
                  onFix: (issue) => _showEditEntry(context, issue),
                  onIgnore: null,
                  onDismiss: (issue) =>
                      _showDismissalConfirmationDialog(context, issue),
                  onTap: (issue) => _showIssueDetails(context, issue),
                  isSelected: false,
                  selectionMode: false,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Build error content
  Widget _buildErrorContent(
    BuildContext context,
    ValidationState state,
  ) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Validation Issues',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            state.errorMessage!,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          LekkyButton(
            text: 'Retry',
            onPressed: () =>
                ref.read(validationControllerProvider.notifier).refresh(),
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
          ),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState(
    BuildContext context,
    ValidationState state,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle_outline,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'No validation issues found',
              style: AppTextStyles.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'All your data is valid and consistent',
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            LekkyButton(
              text: 'Refresh',
              type: LekkyButtonType.primary,
              onPressed: () =>
                  ref.read(validationControllerProvider.notifier).refresh(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build summary card
  Widget _buildSummaryCard(IntegrityReport report) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data Integrity Summary',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const Divider(),
          const SizedBox(height: 8),

          // Entries checked
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Entries Checked:'),
              Text(
                '${report.totalEntriesChecked}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Valid entries
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Valid Entries:'),
              Text(
                '${report.validEntriesCount} (${report.validPercentage.toStringAsFixed(1)}%)',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Invalid entries
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Invalid Entries:'),
              Text(
                '${report.invalidEntriesCount} (${report.invalidPercentage.toStringAsFixed(1)}%)',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Issues by severity
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('High Severity Issues:'),
              Text(
                '${report.highSeverityCount}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Medium Severity Issues:'),
              Text(
                '${report.mediumSeverityCount}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Low Severity Issues:'),
              Text(
                '${report.lowSeverityCount}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Last check time
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Last Check:'),
              Text(
                _formatDateTime(report.generatedAt),
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Show filter dialog
  void _showFilterDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;
    final validationNotifier = ref.read(validationControllerProvider.notifier);
    final currentState = ref.read(validationControllerProvider);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 24,
        insetPadding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: 28,
        ),
        child: Container(
          width: dialogWidth,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.tune,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Filter Issues',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'Close',
                  ),
                ],
              ),
              const SizedBox(height: 24),
              DropdownButtonFormField<ValidationIssueFilterType>(
                value: currentState.filterType,
                decoration: const InputDecoration(
                  labelText: 'Filter by Severity',
                  border: OutlineInputBorder(),
                ),
                items: [
                  ValidationIssueFilterType.all,
                  ValidationIssueFilterType.highSeverity,
                  ValidationIssueFilterType.mediumSeverity,
                  ValidationIssueFilterType.lowSeverity,
                ].map((type) {
                  String label;
                  switch (type) {
                    case ValidationIssueFilterType.all:
                      label = 'All Issues';
                      break;
                    case ValidationIssueFilterType.highSeverity:
                      label = 'High Severity';
                      break;
                    case ValidationIssueFilterType.mediumSeverity:
                      label = 'Medium Severity';
                      break;
                    case ValidationIssueFilterType.lowSeverity:
                      label = 'Low Severity';
                      break;
                  }
                  return DropdownMenuItem<ValidationIssueFilterType>(
                    value: type,
                    child: Text(label),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    validationNotifier.setFilterType(value);
                    Navigator.of(context).pop();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show edit entry dialog for an issue
  Future<void> _showEditEntry(
      BuildContext context, ValidationIssue issue) async {
    final validationNotifier = ref.read(validationControllerProvider.notifier);

    // Get the entry associated with the issue
    final entry = await validationNotifier.getEntryForIssue(issue);

    if (entry == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Entry not found')),
        );
      }
      return;
    }

    if (!context.mounted) return;

    // Get currency symbol from preferences
    final settingsAsync = ref.read(settingsProvider);
    final currencySymbol = settingsAsync.when(
      data: (settings) => settings.currencySymbol,
      loading: () => '£', // Default fallback
      error: (_, __) => '£', // Default fallback
    );

    // Navigate to edit entry based on entry type
    if (entry is MeterReading) {
      await showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: entry,
          topUp: null,
          currencySymbol: currencySymbol,
          onEntryUpdated: () => validationNotifier.refresh(),
          onEntryDeleted: () => validationNotifier.refresh(),
        ),
      );
    } else if (entry is TopUp) {
      await showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: null,
          topUp: entry,
          currencySymbol: currencySymbol,
          onEntryUpdated: () => validationNotifier.refresh(),
          onEntryDeleted: () => validationNotifier.refresh(),
        ),
      );
    }
  }

  /// Show issue details
  Future<void> _showIssueDetails(
      BuildContext context, ValidationIssue issue) async {
    final validationNotifier = ref.read(validationControllerProvider.notifier);

    // Get the entry associated with the issue
    final entry = await validationNotifier.getEntryForIssue(issue);

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (dialogContext) {
        final screenWidth = MediaQuery.of(context).size.width;
        final dialogWidth = _getDialogWidth(context);
        final horizontalPadding = (screenWidth - dialogWidth) / 2;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 24,
          insetPadding: EdgeInsets.symmetric(
            horizontal: horizontalPadding,
            vertical: 28, // Same height as Edit Entry dialog
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildIssueDetailsHeader(context),
                  const SizedBox(height: 24),

                  // Issue type and severity
                  Row(
                    children: [
                      _buildIssueTypeIcon(issue.type),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getIssueTypeText(issue.type),
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      _buildSeverityBadge(issue.severity),
                    ],
                  ),
                  const Divider(height: 24),

                  // Issue message
                  Text(
                    _formatIssueMessage(issue),
                    style: AppTextStyles.bodyMedium,
                  ),

                  // Entry details if available
                  if (entry != null) _buildEntryDetails(entry),

                  // Metadata if available
                  if (issue.metadata != null && issue.metadata!.isNotEmpty)
                    _buildMetadataDetails(issue.metadata!),

                  // Detection date
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Text(
                      'Detected: ${_formatDateTime(issue.detectedAt)}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),
                  _buildIssueDetailsButtonBar(context, issue),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build the issue details dialog header
  Widget _buildIssueDetailsHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.info_outline,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Issue Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build the issue details button bar
  Widget _buildIssueDetailsButtonBar(
      BuildContext context, ValidationIssue issue) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Close',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: (issue.type == ValidationIssueType.missingEntry ||
                  issue.type == ValidationIssueType.duplicateEntry)
              ? LekkyButton(
                  text: 'Dismiss',
                  type: LekkyButtonType.primary,
                  size: LekkyButtonSize.compact,
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showDismissalConfirmationDialog(context, issue);
                  },
                )
              : LekkyButton(
                  text: 'Edit Entry',
                  type: LekkyButtonType.special,
                  size: LekkyButtonSize.compact,
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showEditEntry(context, issue);
                  },
                ),
        ),
      ],
    );
  }

  /// Format a date for display
  String _formatDate(DateTime date) {
    return ref.watch(dateFormatterProvider).formatDateForValidation(date);
  }

  /// Format a date and time for display
  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Build the issue type icon
  Widget _buildIssueTypeIcon(ValidationIssueType type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case ValidationIssueType.negativeValue:
        iconData = Icons.remove_circle_outline;
        iconColor = Colors.red;
        break;
      case ValidationIssueType.futureDate:
        iconData = Icons.event_busy;
        iconColor = Colors.orange;
        break;
      case ValidationIssueType.chronologicalOrder:
        iconData = Icons.swap_vert;
        iconColor = Colors.red;
        break;
      case ValidationIssueType.balanceInconsistency:
        iconData = Icons.account_balance_wallet;
        iconColor = Colors.red;
        break;
      case ValidationIssueType.duplicateEntry:
        iconData = Icons.content_copy;
        iconColor = Colors.orange;
        break;
      case ValidationIssueType.missingEntry:
        iconData = Icons.calendar_today;
        iconColor = Colors.blue;
        break;
      case ValidationIssueType.other:
        iconData = Icons.error_outline;
        iconColor = Colors.red;
        break;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 24,
    );
  }

  /// Build the severity badge
  Widget _buildSeverityBadge(ValidationIssueSeverity severity) {
    Color badgeColor;
    String severityText;

    switch (severity) {
      case ValidationIssueSeverity.high:
        badgeColor = Colors.red;
        severityText = 'High';
        break;
      case ValidationIssueSeverity.medium:
        badgeColor = Colors.orange;
        severityText = 'Medium';
        break;
      case ValidationIssueSeverity.low:
        badgeColor = Colors.blue;
        severityText = 'Low';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        severityText,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Get the issue type text
  String _getIssueTypeText(ValidationIssueType type) {
    switch (type) {
      case ValidationIssueType.negativeValue:
        return 'Negative Value';
      case ValidationIssueType.futureDate:
        return 'Future Date';
      case ValidationIssueType.chronologicalOrder:
        return 'Chronological Order';
      case ValidationIssueType.balanceInconsistency:
        return 'Balance Inconsistency';
      case ValidationIssueType.duplicateEntry:
        return 'Duplicate Entry';
      case ValidationIssueType.missingEntry:
        return 'Missing Entry';
      case ValidationIssueType.other:
        return 'Other Issue';
    }
  }

  /// Build entry details
  Widget _buildEntryDetails(dynamic entry) {
    if (entry == null) {
      return const SizedBox.shrink();
    }

    if (entry is MeterReading) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Text(
            'Meter Reading Details',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text('ID: ${entry.id}'),
          Text('Value: ${entry.value}'),
          Text('Date: ${_formatDate(entry.date)}'),
          Text('Valid: ${entry.isValid ? 'Yes' : 'No'}'),
          if (entry.notes != null && entry.notes!.isNotEmpty)
            Text('Notes: ${entry.notes}'),
        ],
      );
    } else if (entry is TopUp) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Text(
            'Top-up Details',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text('ID: ${entry.id}'),
          Text('Amount: ${entry.amount}'),
          Text('Date: ${_formatDate(entry.date)}'),
          if (entry.notes != null && entry.notes!.isNotEmpty)
            Text('Notes: ${entry.notes}'),
          if (entry.paymentMethod != null && entry.paymentMethod!.isNotEmpty)
            Text('Payment Method: ${entry.paymentMethod}'),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  /// Build metadata details
  Widget _buildMetadataDetails(Map<String, dynamic> metadata) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          'Additional Information',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...metadata.entries.map((entry) {
          return Text('${_formatMetadataKey(entry.key)}: ${entry.value}');
        }),
      ],
    );
  }

  /// Format a metadata key for display
  String _formatMetadataKey(String key) {
    // Convert snake_case to Title Case
    return key
        .split('_')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  /// Format issue message with full detailed description
  String _formatIssueMessage(ValidationIssue issue) {
    // Return the full detailed message from the validation service
    // This ensures users get the complete, helpful description including
    // instructions to check surrounding entries and proper currency formatting
    return issue.message;
  }

  /// Show dismissal confirmation dialog for missing entry issues
  Future<void> _showDismissalConfirmationDialog(
      BuildContext context, ValidationIssue issue) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 24,
        insetPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 28,
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width -
                32, // Account for 16px padding on each side
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed header
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                child: _buildDismissalDialogHeader(dialogContext),
              ),
              // Scrollable content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDismissalDialogContent(issue),
                      const SizedBox(height: 16),
                      _buildDismissalDialogInfoBox(context, issue),
                    ],
                  ),
                ),
              ),
              // Fixed button bar
              Padding(
                padding: const EdgeInsets.all(24),
                child: _buildDismissalDialogButtonBar(dialogContext, issue),
              ),
            ],
          ),
        ),
      ),
    );

    if (confirmed == true) {
      await _performDismissal(issue);
    }
  }

  /// Build the dismissal dialog header
  Widget _buildDismissalDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.visibility_off,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Dismiss Validation Issue',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(false),
          tooltip: 'Cancel',
        ),
      ],
    );
  }

  /// Build the dismissal dialog button bar
  Widget _buildDismissalDialogButtonBar(
      BuildContext context, ValidationIssue issue) {
    final dismissButtonText = issue.type == ValidationIssueType.missingEntry
        ? 'Dismiss Gap'
        : 'Dismiss Entry';

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(false),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: dismissButtonText,
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ),
      ],
    );
  }

  /// Build dismissal dialog content based on issue type
  Widget _buildDismissalDialogContent(ValidationIssue issue) {
    if (issue.type == ValidationIssueType.missingEntry) {
      return Text(
        'This will dismiss the ${issue.metadata!['gap_days']}-day gap by creating a Records Gap entry in your history.',
        style: AppTextStyles.bodyMedium,
        softWrap: true,
        overflow: TextOverflow.visible,
      );
    } else if (issue.type == ValidationIssueType.duplicateEntry) {
      return const Text(
        'This will dismiss the duplicate entry by marking it as ignored. The entry will remain in your history but will be excluded from validation checks.',
        style: AppTextStyles.bodyMedium,
        softWrap: true,
        overflow: TextOverflow.visible,
      );
    }

    return const Text(
      'This will dismiss the validation issue.',
      style: AppTextStyles.bodyMedium,
      softWrap: true,
      overflow: TextOverflow.visible,
    );
  }

  /// Build dismissal dialog info box based on issue type
  Widget _buildDismissalDialogInfoBox(
      BuildContext context, ValidationIssue issue) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What this does:',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          if (issue.type == ValidationIssueType.missingEntry) ...[
            const Text(
              '• Creates a "Records Gap" entry visible in your history\n'
              '• Displays as "No Meter Reading" with "--" amount\n'
              '• Prevents this gap from appearing in future validation checks\n'
              '• Creates breaks in cost trend charts for visual clarity\n'
              '• Does not affect your meter reading calculations\n'
              '• Can be edited or deleted later if needed',
              style: AppTextStyles.bodySmall,
              softWrap: true,
              overflow: TextOverflow.visible,
            ),
          ] else if (issue.type == ValidationIssueType.duplicateEntry) ...[
            const Text(
              '• Marks the duplicate entry as "dismissed" in your history\n'
              '• Entry remains visible but shows as dismissed\n'
              '• Prevents this duplicate from appearing in future validation checks\n'
              '• Allows you to have multiple entries on the same day when needed\n'
              '• Does not affect your calculations or averages\n'
              '• Can be edited or restored later if needed',
              style: AppTextStyles.bodySmall,
              softWrap: true,
              overflow: TextOverflow.visible,
            ),
          ],
        ],
      ),
    );
  }

  /// Perform the dismissal of a validation issue
  Future<void> _performDismissal(ValidationIssue issue) async {
    try {
      // Get the dismissal service
      final dismissalService = serviceLocator<DismissedEntryService>();

      // Dismiss the issue based on type
      if (issue.type == ValidationIssueType.missingEntry) {
        await dismissalService.createDismissalEntry(issue);
      } else if (issue.type == ValidationIssueType.duplicateEntry) {
        await dismissalService.dismissDuplicateEntry(issue);
      } else {
        throw Exception('Unsupported issue type for dismissal: ${issue.type}');
      }

      // Refresh validation data
      await ref.read(validationControllerProvider.notifier).refresh();

      if (mounted) {
        // Show success message based on issue type
        final successMessage = issue.type == ValidationIssueType.missingEntry
            ? 'Records gap dismissed successfully'
            : 'Duplicate entry dismissed successfully';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(successMessage),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Show error message
        final errorMessage = issue.type == ValidationIssueType.missingEntry
            ? 'Error dismissing gap: $e'
            : 'Error dismissing duplicate entry: $e';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Helper function to show validation dialog
void showValidationDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const ValidationDialog(),
  );
}
